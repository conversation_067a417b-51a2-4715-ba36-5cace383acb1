<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Premium React Next.js Developer - Stunning Gig Image</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: #000;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .gig-container {
            width: 1200px;
            height: 675px;
            background: radial-gradient(circle at 30% 20%, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
            border-radius: 32px;
            position: relative;
            overflow: hidden;
            box-shadow: 
                0 0 0 1px rgba(255, 255, 255, 0.1),
                0 30px 60px rgba(0, 0, 0, 0.5),
                0 0 100px rgba(102, 126, 234, 0.4);
        }

        .glass-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(1px);
        }

        .content-wrapper {
            position: relative;
            z-index: 10;
            height: 100%;
            display: flex;
            flex-direction: column;
            padding: 60px;
        }

        .top-section {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 50px;
        }

        .hero-text {
            flex: 1;
            max-width: 600px;
        }

        .badge {
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 8px 20px;
            border-radius: 20px;
            color: #fff;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 20px;
            display: inline-block;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .main-title {
            font-size: 64px;
            font-weight: 900;
            line-height: 1.1;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 50%, #e8f0ff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }

        .subtitle {
            font-size: 24px;
            color: rgba(255, 255, 255, 0.9);
            font-weight: 400;
            line-height: 1.4;
            margin-bottom: 30px;
        }

        .price-container {
            text-align: right;
        }

        .price-badge {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 50%, #ff9a56 100%);
            color: white;
            padding: 25px 40px;
            border-radius: 60px;
            font-size: 32px;
            font-weight: 900;
            box-shadow: 
                0 0 0 4px rgba(255, 255, 255, 0.2),
                0 20px 40px rgba(238, 90, 36, 0.6),
                0 0 80px rgba(255, 107, 107, 0.4);
            animation: glow 3s ease-in-out infinite;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            position: relative;
        }

        .price-badge::before {
            content: '';
            position: absolute;
            top: -3px;
            left: -3px;
            right: -3px;
            bottom: -3px;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24, #ff9a56, #ff6b6b);
            border-radius: 60px;
            z-index: -1;
            animation: rotate 4s linear infinite;
        }

        .middle-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 80px;
            flex: 1;
            align-items: center;
        }

        .tech-showcase {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
        }

        .tech-card {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 25px 20px;
            border-radius: 20px;
            text-align: center;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .tech-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.6s;
        }

        .tech-card:hover::before {
            left: 100%;
        }

        .tech-card:hover {
            transform: translateY(-8px) scale(1.05);
            background: rgba(255, 255, 255, 0.25);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .tech-icon {
            font-size: 32px;
            margin-bottom: 12px;
            display: block;
        }

        .tech-name {
            color: #fff;
            font-size: 16px;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .tech-desc {
            color: rgba(255, 255, 255, 0.8);
            font-size: 12px;
        }

        .visual-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 30px;
        }

        .device-mockup {
            position: relative;
            transform-style: preserve-3d;
        }

        .laptop {
            width: 320px;
            height: 200px;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            border-radius: 20px;
            padding: 20px;
            box-shadow: 
                0 0 0 3px rgba(255, 255, 255, 0.1),
                0 25px 50px rgba(0, 0, 0, 0.4);
            position: relative;
        }

        .laptop-screen {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            position: relative;
            overflow: hidden;
        }

        .phone {
            width: 160px;
            height: 320px;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            border-radius: 25px;
            padding: 15px;
            position: absolute;
            bottom: -40px;
            right: -40px;
            box-shadow: 
                0 0 0 3px rgba(255, 255, 255, 0.1),
                0 20px 40px rgba(0, 0, 0, 0.4);
        }

        .phone-screen {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            width: 100%;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 20px 15px;
            border-radius: 16px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.2);
        }

        .stat-number {
            font-size: 24px;
            font-weight: 900;
            color: #fff;
            display: block;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 500;
        }

        .bottom-section {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 40px;
        }

        .rating-section {
            display: flex;
            align-items: center;
            gap: 20px;
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(20px);
            padding: 20px 30px;
            border-radius: 30px;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .stars {
            color: #ffd700;
            font-size: 24px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .rating-text {
            color: #fff;
            font-weight: 700;
            font-size: 18px;
        }

        .cta-button {
            background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
            color: white;
            border: none;
            padding: 22px 45px;
            border-radius: 30px;
            font-size: 20px;
            font-weight: 800;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 
                0 0 0 3px rgba(255, 255, 255, 0.2),
                0 15px 30px rgba(78, 205, 196, 0.5);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .cta-button:hover {
            transform: translateY(-4px) scale(1.05);
            box-shadow: 
                0 0 0 4px rgba(255, 255, 255, 0.3),
                0 20px 40px rgba(78, 205, 196, 0.7);
        }

        @keyframes glow {
            0%, 100% { 
                box-shadow: 
                    0 0 0 4px rgba(255, 255, 255, 0.2),
                    0 20px 40px rgba(238, 90, 36, 0.6),
                    0 0 80px rgba(255, 107, 107, 0.4);
            }
            50% { 
                box-shadow: 
                    0 0 0 8px rgba(255, 255, 255, 0.3),
                    0 25px 50px rgba(238, 90, 36, 0.8),
                    0 0 120px rgba(255, 107, 107, 0.6);
            }
        }

        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="gig-container">
        <div class="glass-overlay"></div>
        
        <div class="content-wrapper">
            <div class="top-section">
                <div class="hero-text">
                    <div class="badge">⚡ Premium Developer</div>
                    <h1 class="main-title">I'll Build Your<br>Modern Web App</h1>
                    <p class="subtitle">React 18 • Next.js 14 • TypeScript • Full Stack</p>
                </div>
                
                <div class="price-container">
                    <div class="price-badge">From $25</div>
                </div>
            </div>

            <div class="middle-section">
                <div class="tech-showcase">
                    <div class="tech-card">
                        <span class="tech-icon">⚛️</span>
                        <div class="tech-name">React 18</div>
                        <div class="tech-desc">Latest Features</div>
                    </div>
                    <div class="tech-card">
                        <span class="tech-icon">🚀</span>
                        <div class="tech-name">Next.js 14</div>
                        <div class="tech-desc">App Router</div>
                    </div>
                    <div class="tech-card">
                        <span class="tech-icon">📘</span>
                        <div class="tech-name">TypeScript</div>
                        <div class="tech-desc">Type Safety</div>
                    </div>
                    <div class="tech-card">
                        <span class="tech-icon">🎨</span>
                        <div class="tech-name">Tailwind</div>
                        <div class="tech-desc">Modern CSS</div>
                    </div>
                </div>

                <div class="visual-section">
                    <div class="device-mockup">
                        <div class="laptop">
                            <div class="laptop-screen"></div>
                        </div>
                        <div class="phone">
                            <div class="phone-screen"></div>
                        </div>
                    </div>

                    <div class="stats-grid">
                        <div class="stat-card">
                            <span class="stat-number">500+</span>
                            <span class="stat-label">Projects</span>
                        </div>
                        <div class="stat-card">
                            <span class="stat-number">98%</span>
                            <span class="stat-label">Satisfaction</span>
                        </div>
                        <div class="stat-card">
                            <span class="stat-number">24/7</span>
                            <span class="stat-label">Support</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bottom-section">
                <div class="rating-section">
                    <div class="stars">⭐⭐⭐⭐⭐</div>
                    <div class="rating-text">5.0 • 500+ Reviews</div>
                </div>
                <button class="cta-button">Start Project</button>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Animate elements on load
            const elements = [
                { selector: '.badge', delay: 0 },
                { selector: '.main-title', delay: 200 },
                { selector: '.subtitle', delay: 400 },
                { selector: '.price-badge', delay: 600 },
                { selector: '.tech-card', delay: 800 },
                { selector: '.device-mockup', delay: 1000 },
                { selector: '.stat-card', delay: 1200 },
                { selector: '.rating-section', delay: 1400 },
                { selector: '.cta-button', delay: 1600 }
            ];

            elements.forEach(({ selector, delay }) => {
                setTimeout(() => {
                    document.querySelectorAll(selector).forEach((el, index) => {
                        el.style.opacity = '0';
                        el.style.transform = 'translateY(40px)';
                        el.style.transition = 'all 1s cubic-bezier(0.4, 0, 0.2, 1)';
                        
                        setTimeout(() => {
                            el.style.opacity = '1';
                            el.style.transform = 'translateY(0)';
                        }, index * 100);
                    });
                }, delay);
            });

            // Device mockup hover effect
            const deviceMockup = document.querySelector('.device-mockup');
            deviceMockup.addEventListener('mouseenter', function() {
                this.style.transform = 'rotateY(10deg) rotateX(5deg) scale(1.05)';
            });
            
            deviceMockup.addEventListener('mouseleave', function() {
                this.style.transform = 'rotateY(0deg) rotateX(0deg) scale(1)';
            });
        });
    </script>
</body>
</html>
