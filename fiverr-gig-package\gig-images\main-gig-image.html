<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Professional React Next.js Developer - Fiverr Gig</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: #000;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .gig-container {
            width: 1200px;
            height: 675px;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 30%, #16213e 70%, #0f3460 100%);
            position: relative;
            overflow: hidden;
            border-radius: 16px;
            box-shadow:
                0 0 0 1px rgba(255, 255, 255, 0.08),
                0 25px 50px rgba(0, 0, 0, 0.6);
            display: flex;
            flex-direction: column;
        }

        /* Background pattern */
        .bg-pattern {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                radial-gradient(circle at 25% 25%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(255, 119, 198, 0.2) 0%, transparent 50%);
            z-index: 1;
        }

        /* Subtle grid overlay */
        .grid-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                linear-gradient(rgba(255, 255, 255, 0.02) 1px, transparent 1px),
                linear-gradient(90deg, rgba(255, 255, 255, 0.02) 1px, transparent 1px);
            background-size: 40px 40px;
            z-index: 2;
        }

        /* Main content container */
        .content {
            position: relative;
            z-index: 10;
            flex: 1;
            display: grid;
            grid-template-columns: 1fr 1fr;
            align-items: center;
            padding: 50px 60px 20px 60px;
            gap: 60px;
            min-height: 0;
        }

        /* Left side content */
        .left-content {
            display: flex;
            flex-direction: column;
            gap: 24px;
            justify-content: center;
        }

        .badge {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: rgba(120, 119, 198, 0.2);
            border: 1px solid rgba(120, 119, 198, 0.3);
            padding: 8px 16px;
            border-radius: 20px;
            color: #7877c6;
            font-size: 14px;
            font-weight: 600;
            width: fit-content;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .main-title {
            font-size: 42px;
            font-weight: 800;
            line-height: 1.1;
            color: #ffffff;
            margin-bottom: 16px;
        }

        .highlight {
            background: linear-gradient(135deg, #7877c6 0%, #ff77c6 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .description {
            font-size: 16px;
            color: rgba(255, 255, 255, 0.7);
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .tech-stack {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
        }

        .tech-item {
            background: rgba(255, 255, 255, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.12);
            padding: 12px 20px;
            border-radius: 12px;
            color: #ffffff;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .tech-item:hover {
            background: rgba(120, 119, 198, 0.2);
            border-color: rgba(120, 119, 198, 0.4);
            transform: translateY(-2px);
        }

        .cta-section {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .price-tag {
            background: linear-gradient(135deg, #7877c6 0%, #ff77c6 100%);
            color: #ffffff;
            padding: 16px 24px;
            border-radius: 12px;
            font-size: 24px;
            font-weight: 700;
            box-shadow: 0 8px 32px rgba(120, 119, 198, 0.4);
        }

        .cta-button {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: #ffffff;
            padding: 16px 32px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .cta-button:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
        }

        /* Right side content */
        .right-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 30px;
            min-height: 400px;
        }

        .visual-showcase {
            position: relative;
            width: 100%;
            max-width: 450px;
            height: 300px;
        }

        .browser-window {
            width: 100%;
            height: 260px;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .browser-header {
            height: 40px;
            background: rgba(255, 255, 255, 0.08);
            display: flex;
            align-items: center;
            padding: 0 16px;
            gap: 8px;
        }

        .browser-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }

        .dot-red { background: #ff5f56; }
        .dot-yellow { background: #ffbd2e; }
        .dot-green { background: #27ca3f; }

        .browser-content {
            height: calc(100% - 40px);
            background: linear-gradient(135deg, #7877c6 0%, #ff77c6 50%, #7877c6 100%);
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .code-preview {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            color: #ffffff;
            line-height: 1.6;
        }

        .mobile-frame {
            position: absolute;
            bottom: -20px;
            right: -20px;
            width: 120px;
            height: 240px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 12px;
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
        }

        .mobile-screen {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #7877c6 0%, #ff77c6 100%);
            border-radius: 12px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 16px;
            width: 100%;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            padding: 20px 16px;
            border-radius: 12px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            background: rgba(255, 255, 255, 0.08);
            transform: translateY(-2px);
        }

        .stat-number {
            font-size: 24px;
            font-weight: 700;
            color: #7877c6;
            display: block;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
            font-weight: 500;
        }

        /* Bottom section */
        .bottom-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 60px 40px 60px;
            z-index: 15;
        }

        .rating-badge {
            display: flex;
            align-items: center;
            gap: 12px;
            background: rgba(255, 255, 255, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.12);
            padding: 12px 20px;
            border-radius: 20px;
        }

        .stars {
            color: #ffd700;
            font-size: 16px;
        }

        .rating-text {
            color: #ffffff;
            font-size: 14px;
            font-weight: 600;
        }

        .delivery-badge {
            background: rgba(120, 119, 198, 0.2);
            border: 1px solid rgba(120, 119, 198, 0.3);
            color: #7877c6;
            padding: 12px 20px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .content {
                grid-template-columns: 1fr;
                padding: 40px;
                gap: 40px;
            }

            .main-title {
                font-size: 36px;
            }

            .visual-showcase {
                max-width: 100%;
                height: 240px;
            }

            .bottom-info {
                position: static;
                margin-top: 30px;
                flex-direction: column;
                gap: 16px;
            }
        }

    </style>
</head>
<body>
    <div class="gig-container">
        <!-- Background elements -->
        <div class="bg-pattern"></div>
        <div class="grid-overlay"></div>

        <!-- Main content -->
        <div class="content">
            <!-- Left side -->
            <div class="left-content">
                <div class="badge">
                    <span>●</span> Available Now
                </div>

                <h1 class="main-title">
                    I'll Build Your<br>
                    <span class="highlight">Modern Web App</span>
                </h1>

                <p class="description">
                    Professional React & Next.js development with TypeScript.
                    Fast, secure, and scalable web applications that drive results.
                </p>

                <div class="tech-stack">
                    <div class="tech-item">React 18</div>
                    <div class="tech-item">Next.js 14</div>
                    <div class="tech-item">TypeScript</div>
                    <div class="tech-item">Tailwind CSS</div>
                    <div class="tech-item">PostgreSQL</div>
                    <div class="tech-item">Prisma ORM</div>
                </div>

                <div class="cta-section">
                    <div class="price-tag">From $25</div>
                    <button class="cta-button">Get Started</button>
                </div>
            </div>

            <!-- Right side -->
            <div class="right-content">
                <div class="visual-showcase">
                    <div class="browser-window">
                        <div class="browser-header">
                            <div class="browser-dot dot-red"></div>
                            <div class="browser-dot dot-yellow"></div>
                            <div class="browser-dot dot-green"></div>
                        </div>
                        <div class="browser-content">
                            <div class="code-preview">
                                <div>const App = () => {</div>
                                <div>  return &lt;YourDreamApp /&gt;</div>
                                <div>}</div>
                            </div>
                        </div>
                    </div>

                    <div class="mobile-frame">
                        <div class="mobile-screen"></div>
                    </div>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <span class="stat-number">500+</span>
                        <span class="stat-label">Projects</span>
                    </div>
                    <div class="stat-card">
                        <span class="stat-number">98%</span>
                        <span class="stat-label">Satisfaction</span>
                    </div>
                    <div class="stat-card">
                        <span class="stat-number">24h</span>
                        <span class="stat-label">Delivery</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bottom info -->
        <div class="bottom-info">
            <div class="rating-badge">
                <div class="stars">★★★★★</div>
                <div class="rating-text">5.0 • 500+ Reviews</div>
            </div>
            <div class="delivery-badge">Fast 24h Delivery</div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Animate elements on load
            const leftElements = document.querySelectorAll('.left-content > *');
            const rightElements = document.querySelectorAll('.right-content > *');

            leftElements.forEach((el, index) => {
                el.style.opacity = '0';
                el.style.transform = 'translateX(-30px)';
                el.style.transition = 'all 0.8s cubic-bezier(0.4, 0, 0.2, 1)';

                setTimeout(() => {
                    el.style.opacity = '1';
                    el.style.transform = 'translateX(0)';
                }, index * 150);
            });

            rightElements.forEach((el, index) => {
                el.style.opacity = '0';
                el.style.transform = 'translateX(30px)';
                el.style.transition = 'all 0.8s cubic-bezier(0.4, 0, 0.2, 1)';

                setTimeout(() => {
                    el.style.opacity = '1';
                    el.style.transform = 'translateX(0)';
                }, 600 + index * 150);
            });

            // Bottom info animation
            const bottomInfo = document.querySelector('.bottom-info');
            bottomInfo.style.opacity = '0';
            bottomInfo.style.transform = 'translateY(20px)';
            bottomInfo.style.transition = 'all 0.8s cubic-bezier(0.4, 0, 0.2, 1)';

            setTimeout(() => {
                bottomInfo.style.opacity = '1';
                bottomInfo.style.transform = 'translateY(0)';
            }, 1200);

            // Interactive hover effects
            document.querySelectorAll('.tech-item').forEach(item => {
                item.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px) scale(1.02)';
                });

                item.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            document.querySelectorAll('.stat-card').forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-4px)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        });
    </script>
</body>
</html>
    </style>
</head>
<body>
    <div class="gig-container">
        <!-- Animated background elements -->
        <div class="bg-elements">
            <div class="bg-circle"></div>
            <div class="bg-circle"></div>
            <div class="bg-circle"></div>
        </div>

        <!-- Price badge -->
        <div class="price-badge">Starting $25</div>

        <!-- Main content -->
        <div class="main-content">
            <div class="header">
                <h1 class="title">I'll Build Your Dream<br>React Web App</h1>
                <p class="subtitle">Modern • Fast • Professional • TypeScript Ready</p>
            </div>

            <div class="content-grid">
                <div class="left-content">
                    <div class="tech-stack">
                        <div class="tech-badge">⚛️ React 18</div>
                        <div class="tech-badge">🚀 Next.js 14</div>
                        <div class="tech-badge">📘 TypeScript</div>
                        <div class="tech-badge">🎨 Tailwind CSS</div>
                        <div class="tech-badge">🗄️ PostgreSQL</div>
                        <div class="tech-badge">🔧 Prisma ORM</div>
                    </div>
                </div>

                <div class="right-content">
                    <div class="mockup-container">
                        <div class="phone-mockup">
                            <div class="phone-screen"></div>
                        </div>
                        <div class="laptop-mockup">
                            <div class="laptop-screen"></div>
                        </div>
                    </div>

                    <div class="features">
                        <div class="feature">
                            <span class="feature-icon">⚡</span>
                            <div class="feature-title">Lightning Fast</div>
                            <div class="feature-desc">90+ PageSpeed Score</div>
                        </div>
                        <div class="feature">
                            <span class="feature-icon">📱</span>
                            <div class="feature-title">Mobile First</div>
                            <div class="feature-desc">100% Responsive</div>
                        </div>
                        <div class="feature">
                            <span class="feature-icon">🔒</span>
                            <div class="feature-title">Secure Code</div>
                            <div class="feature-desc">Enterprise Grade</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="cta-section">
                <div class="rating">
                    <div class="stars">⭐⭐⭐⭐⭐</div>
                    <div class="rating-text">5.0 • 500+ Reviews</div>
                </div>
                <button class="cta-button">Order Now</button>
            </div>
        </div>
    </div>

    <script>
        // Enhanced interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            // Tech badge hover effects
            document.querySelectorAll('.tech-badge').forEach((badge, index) => {
                badge.style.animationDelay = `${index * 0.1}s`;

                badge.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-3px) scale(1.02)';
                });

                badge.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // Feature hover effects
            document.querySelectorAll('.feature').forEach(feature => {
                feature.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px) scale(1.05)';
                });

                feature.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // Animate elements on load
            const animateElements = [
                { selector: '.title', delay: 0 },
                { selector: '.subtitle', delay: 200 },
                { selector: '.tech-badge', delay: 400 },
                { selector: '.feature', delay: 600 },
                { selector: '.rating', delay: 800 },
                { selector: '.cta-button', delay: 1000 }
            ];

            animateElements.forEach(({ selector, delay }) => {
                setTimeout(() => {
                    document.querySelectorAll(selector).forEach((el, index) => {
                        el.style.opacity = '0';
                        el.style.transform = 'translateY(30px)';
                        el.style.transition = 'all 0.8s cubic-bezier(0.4, 0, 0.2, 1)';

                        setTimeout(() => {
                            el.style.opacity = '1';
                            el.style.transform = 'translateY(0)';
                        }, index * 100);
                    });
                }, delay);
            });

            // Mockup hover effect
            const mockupContainer = document.querySelector('.mockup-container');
            if (mockupContainer) {
                mockupContainer.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.05) rotateY(5deg)';
                });

                mockupContainer.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1) rotateY(0deg)';
                });
            }

            // CTA button pulse effect
            const ctaButton = document.querySelector('.cta-button');
            if (ctaButton) {
                setInterval(() => {
                    ctaButton.style.transform = 'scale(1.02)';
                    setTimeout(() => {
                        ctaButton.style.transform = 'scale(1)';
                    }, 200);
                }, 3000);
            }
        });
    </script>
</body>
</html>
