<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stunning React Next.js Web App Development - Gig Image</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: #0a0a0a;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .gig-container {
            width: 1200px;
            height: 675px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            border-radius: 24px;
            position: relative;
            overflow: hidden;
            box-shadow:
                0 0 0 1px rgba(255, 255, 255, 0.1),
                0 20px 40px rgba(0, 0, 0, 0.4),
                0 40px 80px rgba(102, 126, 234, 0.3);
        }

        /* Animated background elements */
        .bg-elements {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }

        .bg-circle {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            animation: float 8s ease-in-out infinite;
        }

        .bg-circle:nth-child(1) {
            width: 300px;
            height: 300px;
            top: -150px;
            right: -150px;
            animation-delay: 0s;
        }

        .bg-circle:nth-child(2) {
            width: 200px;
            height: 200px;
            bottom: -100px;
            left: -100px;
            animation-delay: 2s;
        }

        .bg-circle:nth-child(3) {
            width: 150px;
            height: 150px;
            top: 50%;
            right: 10%;
            animation-delay: 4s;
        }

        .main-content {
            position: relative;
            z-index: 10;
            height: 100%;
            display: flex;
            flex-direction: column;
            padding: 50px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .title {
            font-size: 56px;
            font-weight: 900;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 50%, #e8f0ff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 15px;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
            line-height: 1.1;
        }

        .subtitle {
            font-size: 22px;
            color: rgba(255, 255, 255, 0.9);
            font-weight: 400;
            letter-spacing: 0.5px;
        }

        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 60px;
            flex: 1;
            align-items: center;
        }

        .left-content {
            display: flex;
            flex-direction: column;
            gap: 30px;
        }

        .tech-stack {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }

        .tech-badge {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 16px 24px;
            border-radius: 16px;
            color: #ffffff;
            font-weight: 600;
            font-size: 15px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .tech-badge::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.6s;
        }

        .tech-badge:hover::before {
            left: 100%;
        }

        .tech-badge:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: translateY(-3px) scale(1.02);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
        }

        .right-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 30px;
        }

        .mockup-container {
            position: relative;
            width: 100%;
            max-width: 400px;
        }

        .phone-mockup {
            width: 200px;
            height: 400px;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            border-radius: 30px;
            padding: 20px;
            position: relative;
            box-shadow:
                0 0 0 8px rgba(255, 255, 255, 0.1),
                0 20px 40px rgba(0, 0, 0, 0.4);
            margin: 0 auto;
        }

        .phone-screen {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            position: relative;
            overflow: hidden;
        }

        .laptop-mockup {
            width: 300px;
            height: 200px;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            border-radius: 15px;
            padding: 15px;
            position: absolute;
            top: 50px;
            right: -50px;
            box-shadow:
                0 0 0 3px rgba(255, 255, 255, 0.1),
                0 15px 30px rgba(0, 0, 0, 0.3);
        }

        .laptop-screen {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 8px;
            position: relative;
            overflow: hidden;
        }

        .features {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            width: 100%;
        }

        .feature {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 20px 15px;
            border-radius: 16px;
            text-align: center;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
        }

        .feature:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-8px) scale(1.05);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .feature-icon {
            font-size: 28px;
            margin-bottom: 8px;
            display: block;
        }

        .feature-title {
            color: #ffffff;
            font-size: 14px;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .feature-desc {
            color: rgba(255, 255, 255, 0.8);
            font-size: 11px;
            line-height: 1.3;
        }

        .price-badge {
            position: absolute;
            top: 50px;
            right: 50px;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 50%, #ff9a56 100%);
            color: white;
            padding: 20px 30px;
            border-radius: 50px;
            font-size: 28px;
            font-weight: 900;
            box-shadow:
                0 0 0 3px rgba(255, 255, 255, 0.2),
                0 15px 30px rgba(238, 90, 36, 0.5),
                0 0 50px rgba(255, 107, 107, 0.3);
            animation: pulse 3s ease-in-out infinite;
            z-index: 20;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .price-badge::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24, #ff9a56, #ff6b6b);
            border-radius: 50px;
            z-index: -1;
            animation: rotate 4s linear infinite;
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
                box-shadow:
                    0 0 0 3px rgba(255, 255, 255, 0.2),
                    0 15px 30px rgba(238, 90, 36, 0.5),
                    0 0 50px rgba(255, 107, 107, 0.3);
            }
            50% {
                transform: scale(1.08);
                box-shadow:
                    0 0 0 6px rgba(255, 255, 255, 0.3),
                    0 20px 40px rgba(238, 90, 36, 0.7),
                    0 0 80px rgba(255, 107, 107, 0.5);
            }
        }

        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg) scale(1);
                opacity: 0.6;
            }
            50% {
                transform: translateY(-30px) rotate(180deg) scale(1.1);
                opacity: 0.8;
            }
        }

        .cta-section {
            position: absolute;
            bottom: 40px;
            left: 50px;
            right: 50px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .rating {
            display: flex;
            align-items: center;
            gap: 15px;
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(20px);
            padding: 15px 25px;
            border-radius: 25px;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .stars {
            color: #ffd700;
            font-size: 20px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .rating-text {
            color: #ffffff;
            font-weight: 600;
            font-size: 16px;
        }

        .cta-button {
            background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
            color: white;
            border: none;
            padding: 18px 35px;
            border-radius: 25px;
            font-size: 18px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow:
                0 0 0 2px rgba(255, 255, 255, 0.2),
                0 10px 20px rgba(78, 205, 196, 0.4);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .cta-button:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow:
                0 0 0 3px rgba(255, 255, 255, 0.3),
                0 15px 30px rgba(78, 205, 196, 0.6);
            background: linear-gradient(135deg, #5ee7df 0%, #4fb3a6 100%);
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .gig-container {
                width: 100%;
                height: auto;
                min-height: 675px;
            }

            .content-grid {
                grid-template-columns: 1fr;
                gap: 30px;
            }

            .title {
                font-size: 42px;
            }

            .price-badge {
                position: static;
                margin: 0 auto 20px;
                display: block;
                width: fit-content;
            }
        }
    </style>
</head>
<body>
    <div class="gig-container">
        <!-- Animated background elements -->
        <div class="bg-elements">
            <div class="bg-circle"></div>
            <div class="bg-circle"></div>
            <div class="bg-circle"></div>
        </div>

        <!-- Price badge -->
        <div class="price-badge">Starting $25</div>

        <!-- Main content -->
        <div class="main-content">
            <div class="header">
                <h1 class="title">I'll Build Your Dream<br>React Web App</h1>
                <p class="subtitle">Modern • Fast • Professional • TypeScript Ready</p>
            </div>

            <div class="content-grid">
                <div class="left-content">
                    <div class="tech-stack">
                        <div class="tech-badge">⚛️ React 18</div>
                        <div class="tech-badge">🚀 Next.js 14</div>
                        <div class="tech-badge">📘 TypeScript</div>
                        <div class="tech-badge">🎨 Tailwind CSS</div>
                        <div class="tech-badge">🗄️ PostgreSQL</div>
                        <div class="tech-badge">🔧 Prisma ORM</div>
                    </div>
                </div>

                <div class="right-content">
                    <div class="mockup-container">
                        <div class="phone-mockup">
                            <div class="phone-screen"></div>
                        </div>
                        <div class="laptop-mockup">
                            <div class="laptop-screen"></div>
                        </div>
                    </div>

                    <div class="features">
                        <div class="feature">
                            <span class="feature-icon">⚡</span>
                            <div class="feature-title">Lightning Fast</div>
                            <div class="feature-desc">90+ PageSpeed Score</div>
                        </div>
                        <div class="feature">
                            <span class="feature-icon">📱</span>
                            <div class="feature-title">Mobile First</div>
                            <div class="feature-desc">100% Responsive</div>
                        </div>
                        <div class="feature">
                            <span class="feature-icon">🔒</span>
                            <div class="feature-title">Secure Code</div>
                            <div class="feature-desc">Enterprise Grade</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="cta-section">
                <div class="rating">
                    <div class="stars">⭐⭐⭐⭐⭐</div>
                    <div class="rating-text">5.0 • 500+ Reviews</div>
                </div>
                <button class="cta-button">Order Now</button>
            </div>
        </div>
    </div>

    <script>
        // Enhanced interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            // Tech badge hover effects
            document.querySelectorAll('.tech-badge').forEach((badge, index) => {
                badge.style.animationDelay = `${index * 0.1}s`;

                badge.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-3px) scale(1.02)';
                });

                badge.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // Feature hover effects
            document.querySelectorAll('.feature').forEach(feature => {
                feature.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px) scale(1.05)';
                });

                feature.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // Animate elements on load
            const animateElements = [
                { selector: '.title', delay: 0 },
                { selector: '.subtitle', delay: 200 },
                { selector: '.tech-badge', delay: 400 },
                { selector: '.feature', delay: 600 },
                { selector: '.rating', delay: 800 },
                { selector: '.cta-button', delay: 1000 }
            ];

            animateElements.forEach(({ selector, delay }) => {
                setTimeout(() => {
                    document.querySelectorAll(selector).forEach((el, index) => {
                        el.style.opacity = '0';
                        el.style.transform = 'translateY(30px)';
                        el.style.transition = 'all 0.8s cubic-bezier(0.4, 0, 0.2, 1)';

                        setTimeout(() => {
                            el.style.opacity = '1';
                            el.style.transform = 'translateY(0)';
                        }, index * 100);
                    });
                }, delay);
            });

            // Mockup hover effect
            const mockupContainer = document.querySelector('.mockup-container');
            if (mockupContainer) {
                mockupContainer.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.05) rotateY(5deg)';
                });

                mockupContainer.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1) rotateY(0deg)';
                });
            }

            // CTA button pulse effect
            const ctaButton = document.querySelector('.cta-button');
            if (ctaButton) {
                setInterval(() => {
                    ctaButton.style.transform = 'scale(1.02)';
                    setTimeout(() => {
                        ctaButton.style.transform = 'scale(1)';
                    }, 200);
                }, 3000);
            }
        });
    </script>
</body>
</html>
