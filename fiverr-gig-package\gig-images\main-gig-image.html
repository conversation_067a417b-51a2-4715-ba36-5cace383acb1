<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>React Next.js Web App Development - Main Gig Image</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .gig-container {
            width: 1200px;
            height: 675px;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            border-radius: 20px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
        }

        .header {
            position: absolute;
            top: 40px;
            left: 50px;
            right: 50px;
            z-index: 10;
        }

        .title {
            font-size: 48px;
            font-weight: 800;
            color: #ffffff;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .subtitle {
            font-size: 24px;
            color: #a8d8ff;
            font-weight: 300;
        }

        .tech-stack {
            position: absolute;
            top: 180px;
            left: 50px;
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }

        .tech-badge {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 12px 20px;
            border-radius: 25px;
            color: #ffffff;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .tech-badge:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .features {
            position: absolute;
            bottom: 100px;
            left: 50px;
            right: 50px;
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 30px;
        }

        .feature {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .feature:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-5px);
        }

        .feature-icon {
            font-size: 32px;
            margin-bottom: 10px;
        }

        .feature-title {
            color: #ffffff;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .feature-desc {
            color: #a8d8ff;
            font-size: 14px;
        }

        .price-badge {
            position: absolute;
            top: 40px;
            right: 50px;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 15px 25px;
            border-radius: 50px;
            font-size: 24px;
            font-weight: 700;
            box-shadow: 0 10px 25px rgba(238, 90, 36, 0.4);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .floating-elements {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            pointer-events: none;
        }

        .floating-element {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        .floating-element:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 20%;
            right: 15%;
            animation-delay: 0s;
        }

        .floating-element:nth-child(2) {
            width: 60px;
            height: 60px;
            top: 60%;
            right: 25%;
            animation-delay: 2s;
        }

        .floating-element:nth-child(3) {
            width: 100px;
            height: 100px;
            top: 40%;
            right: 5%;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .rating {
            position: absolute;
            bottom: 30px;
            right: 50px;
            display: flex;
            align-items: center;
            gap: 10px;
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            padding: 10px 20px;
            border-radius: 25px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .stars {
            color: #ffd700;
            font-size: 20px;
        }

        .rating-text {
            color: #ffffff;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="gig-container">
        <div class="floating-elements">
            <div class="floating-element"></div>
            <div class="floating-element"></div>
            <div class="floating-element"></div>
        </div>

        <div class="header">
            <h1 class="title">Modern React & Next.js</h1>
            <p class="subtitle">Professional Web App Development with TypeScript</p>
        </div>

        <div class="price-badge">Starting $25</div>

        <div class="tech-stack">
            <div class="tech-badge">React 18</div>
            <div class="tech-badge">Next.js 14</div>
            <div class="tech-badge">TypeScript</div>
            <div class="tech-badge">Tailwind CSS</div>
            <div class="tech-badge">PostgreSQL</div>
            <div class="tech-badge">Prisma ORM</div>
        </div>

        <div class="features">
            <div class="feature">
                <div class="feature-icon">🚀</div>
                <div class="feature-title">Lightning Fast</div>
                <div class="feature-desc">Optimized for speed & performance</div>
            </div>
            <div class="feature">
                <div class="feature-icon">📱</div>
                <div class="feature-title">Fully Responsive</div>
                <div class="feature-desc">Perfect on all devices</div>
            </div>
            <div class="feature">
                <div class="feature-icon">🔒</div>
                <div class="feature-title">Secure & Scalable</div>
                <div class="feature-desc">Enterprise-grade security</div>
            </div>
        </div>

        <div class="rating">
            <div class="stars">⭐⭐⭐⭐⭐</div>
            <div class="rating-text">5.0 (500+ Reviews)</div>
        </div>
    </div>

    <script>
        // Add interactive hover effects
        document.querySelectorAll('.tech-badge').forEach(badge => {
            badge.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-3px) scale(1.05)';
            });
            
            badge.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });

        // Animate features on load
        window.addEventListener('load', function() {
            const features = document.querySelectorAll('.feature');
            features.forEach((feature, index) => {
                setTimeout(() => {
                    feature.style.opacity = '0';
                    feature.style.transform = 'translateY(20px)';
                    feature.style.transition = 'all 0.6s ease';
                    
                    setTimeout(() => {
                        feature.style.opacity = '1';
                        feature.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 200);
            });
        });
    </script>
</body>
</html>
