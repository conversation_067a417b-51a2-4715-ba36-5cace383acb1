<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pricing Comparison - Web Development Packages</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .pricing-container {
            width: 1200px;
            height: 675px;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
            border-radius: 20px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
            padding: 40px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .title {
            font-size: 42px;
            font-weight: 700;
            color: #ffffff;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .subtitle {
            font-size: 20px;
            color: #a8d8ff;
            font-weight: 300;
        }

        .packages-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 30px;
            height: calc(100% - 140px);
        }

        .package {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 30px 25px;
            position: relative;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
        }

        .package:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            background: rgba(255, 255, 255, 0.15);
        }

        .package.popular {
            border-color: #ff6b6b;
            background: rgba(255, 107, 107, 0.15);
            transform: scale(1.05);
        }

        .package.popular::before {
            content: "MOST POPULAR";
            position: absolute;
            top: -15px;
            left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 8px 20px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 700;
            letter-spacing: 1px;
        }

        .package-name {
            font-size: 24px;
            font-weight: 700;
            color: #ffffff;
            text-align: center;
            margin-bottom: 10px;
        }

        .package-price {
            font-size: 48px;
            font-weight: 800;
            color: #ffffff;
            text-align: center;
            margin-bottom: 5px;
        }

        .package-price span {
            font-size: 18px;
            color: #a8d8ff;
            font-weight: 400;
        }

        .package-delivery {
            text-align: center;
            color: #a8d8ff;
            font-size: 14px;
            margin-bottom: 25px;
        }

        .features-list {
            flex: 1;
            list-style: none;
        }

        .features-list li {
            color: #ffffff;
            font-size: 14px;
            margin-bottom: 12px;
            padding-left: 25px;
            position: relative;
            line-height: 1.4;
        }

        .features-list li::before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #4ecdc4;
            font-weight: 700;
            font-size: 16px;
        }

        .features-list li.unavailable {
            color: #666;
            text-decoration: line-through;
        }

        .features-list li.unavailable::before {
            content: "✗";
            color: #ff6b6b;
        }

        .package-button {
            background: linear-gradient(135deg, #4ecdc4, #44a08d);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 20px;
        }

        .package-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(78, 205, 196, 0.3);
        }

        .popular .package-button {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
        }

        .popular .package-button:hover {
            box-shadow: 0 10px 20px rgba(255, 107, 107, 0.3);
        }

        .tech-stack {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 15px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 15px 30px;
            border-radius: 25px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .tech-badge {
            color: #ffffff;
            font-size: 14px;
            font-weight: 600;
            padding: 5px 12px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 15px;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .package {
            animation: slideInUp 0.6s ease forwards;
        }

        .package:nth-child(1) { animation-delay: 0.1s; }
        .package:nth-child(2) { animation-delay: 0.3s; }
        .package:nth-child(3) { animation-delay: 0.5s; }
    </style>
</head>
<body>
    <div class="pricing-container">
        <div class="header">
            <h1 class="title">Choose Your Perfect Package</h1>
            <p class="subtitle">Professional React & Next.js Web Development</p>
        </div>

        <div class="packages-grid">
            <!-- Basic Package -->
            <div class="package">
                <div class="package-name">BASIC</div>
                <div class="package-price">$25<span>/project</span></div>
                <div class="package-delivery">3 Days Delivery</div>
                
                <ul class="features-list">
                    <li>Single-page React app</li>
                    <li>Responsive design</li>
                    <li>TypeScript setup</li>
                    <li>Tailwind CSS styling</li>
                    <li>Contact form integration</li>
                    <li>SEO meta tags</li>
                    <li>Source code delivery</li>
                    <li>2 revisions included</li>
                    <li class="unavailable">Database integration</li>
                    <li class="unavailable">User authentication</li>
                    <li class="unavailable">Admin dashboard</li>
                    <li class="unavailable">Payment integration</li>
                </ul>
                
                <button class="package-button">Get Started</button>
            </div>

            <!-- Standard Package -->
            <div class="package popular">
                <div class="package-name">STANDARD</div>
                <div class="package-price">$75<span>/project</span></div>
                <div class="package-delivery">7 Days Delivery</div>
                
                <ul class="features-list">
                    <li>Multi-page Next.js app</li>
                    <li>Full TypeScript implementation</li>
                    <li>Database integration</li>
                    <li>User authentication</li>
                    <li>Admin dashboard</li>
                    <li>RESTful API development</li>
                    <li>Responsive design</li>
                    <li>SEO optimization</li>
                    <li>Email notifications</li>
                    <li>File upload functionality</li>
                    <li>3 revisions included</li>
                    <li>30 days free support</li>
                </ul>
                
                <button class="package-button">Most Popular</button>
            </div>

            <!-- Premium Package -->
            <div class="package">
                <div class="package-name">PREMIUM</div>
                <div class="package-price">$150<span>/project</span></div>
                <div class="package-delivery">14 Days Delivery</div>
                
                <ul class="features-list">
                    <li>Advanced Next.js application</li>
                    <li>Full-stack TypeScript</li>
                    <li>Multiple database integration</li>
                    <li>Advanced authentication</li>
                    <li>Payment gateway integration</li>
                    <li>Real-time features</li>
                    <li>Advanced admin panel</li>
                    <li>API documentation</li>
                    <li>Comprehensive testing</li>
                    <li>CI/CD pipeline setup</li>
                    <li>PWA capabilities</li>
                    <li>60 days free support</li>
                </ul>
                
                <button class="package-button">Go Premium</button>
            </div>
        </div>

        <div class="tech-stack">
            <div class="tech-badge">React 18</div>
            <div class="tech-badge">Next.js 14</div>
            <div class="tech-badge">TypeScript</div>
            <div class="tech-badge">Tailwind CSS</div>
            <div class="tech-badge">PostgreSQL</div>
        </div>
    </div>

    <script>
        // Add interactive effects
        document.querySelectorAll('.package').forEach(package => {
            package.addEventListener('mouseenter', function() {
                if (!this.classList.contains('popular')) {
                    this.style.transform = 'translateY(-15px) scale(1.02)';
                }
            });
            
            package.addEventListener('mouseleave', function() {
                if (!this.classList.contains('popular')) {
                    this.style.transform = 'translateY(0) scale(1)';
                } else {
                    this.style.transform = 'translateY(-10px) scale(1.05)';
                }
            });
        });

        // Animate on load
        window.addEventListener('load', function() {
            const packages = document.querySelectorAll('.package');
            packages.forEach((pkg, index) => {
                setTimeout(() => {
                    pkg.style.opacity = '0';
                    pkg.style.transform = 'translateY(30px)';
                    pkg.style.transition = 'all 0.6s ease';
                    
                    setTimeout(() => {
                        pkg.style.opacity = '1';
                        if (pkg.classList.contains('popular')) {
                            pkg.style.transform = 'translateY(-10px) scale(1.05)';
                        } else {
                            pkg.style.transform = 'translateY(0) scale(1)';
                        }
                    }, 100);
                }, index * 200);
            });
        });
    </script>
</body>
</html>
