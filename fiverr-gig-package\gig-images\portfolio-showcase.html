<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portfolio Showcase - Web App Development</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .showcase-container {
            width: 1200px;
            height: 675px;
            background: linear-gradient(135deg, #0a0a1a 0%, #1a1a2e 30%, #1e3a5f 70%, #2c5282 100%);
            border-radius: 20px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
        }

        .header {
            position: absolute;
            top: 30px;
            left: 50px;
            right: 50px;
            text-align: center;
            z-index: 10;
        }

        .title {
            font-size: 36px;
            font-weight: 700;
            color: #ffffff;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .subtitle {
            font-size: 18px;
            color: #a8d8ff;
            font-weight: 300;
        }

        .portfolio-grid {
            position: absolute;
            top: 120px;
            left: 50px;
            right: 50px;
            bottom: 80px;
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
        }

        .portfolio-item {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            overflow: hidden;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }

        .portfolio-item:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            background: rgba(255, 255, 255, 0.15);
        }

        .portfolio-image {
            width: 100%;
            height: 60%;
            background: linear-gradient(45deg, #667eea, #764ba2);
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            color: white;
        }

        .portfolio-item:nth-child(1) .portfolio-image {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
        }

        .portfolio-item:nth-child(2) .portfolio-image {
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
        }

        .portfolio-item:nth-child(3) .portfolio-image {
            background: linear-gradient(45deg, #a8edea, #fed6e3);
        }

        .portfolio-item:nth-child(4) .portfolio-image {
            background: linear-gradient(45deg, #ffecd2, #fcb69f);
        }

        .portfolio-item:nth-child(5) .portfolio-image {
            background: linear-gradient(45deg, #667eea, #764ba2);
        }

        .portfolio-item:nth-child(6) .portfolio-image {
            background: linear-gradient(45deg, #f093fb, #f5576c);
        }

        .portfolio-content {
            padding: 15px;
            height: 40%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .portfolio-title {
            color: #ffffff;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .portfolio-desc {
            color: #a8d8ff;
            font-size: 12px;
            line-height: 1.4;
            margin-bottom: 10px;
        }

        .portfolio-tech {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }

        .tech-tag {
            background: rgba(255, 255, 255, 0.2);
            color: #ffffff;
            padding: 3px 8px;
            border-radius: 10px;
            font-size: 10px;
            font-weight: 500;
        }

        .stats {
            position: absolute;
            bottom: 20px;
            left: 50px;
            right: 50px;
            display: flex;
            justify-content: space-around;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 15px;
        }

        .stat {
            text-align: center;
        }

        .stat-number {
            color: #ffffff;
            font-size: 24px;
            font-weight: 700;
            display: block;
        }

        .stat-label {
            color: #a8d8ff;
            font-size: 12px;
            font-weight: 500;
        }

        .overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: all 0.3s ease;
            color: white;
            font-size: 14px;
            font-weight: 600;
            text-align: center;
            padding: 20px;
        }

        .portfolio-item:hover .overlay {
            opacity: 1;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .portfolio-item {
            animation: fadeInUp 0.6s ease forwards;
        }

        .portfolio-item:nth-child(1) { animation-delay: 0.1s; }
        .portfolio-item:nth-child(2) { animation-delay: 0.2s; }
        .portfolio-item:nth-child(3) { animation-delay: 0.3s; }
        .portfolio-item:nth-child(4) { animation-delay: 0.4s; }
        .portfolio-item:nth-child(5) { animation-delay: 0.5s; }
        .portfolio-item:nth-child(6) { animation-delay: 0.6s; }
    </style>
</head>
<body>
    <div class="showcase-container">
        <div class="header">
            <h1 class="title">Recent Projects Portfolio</h1>
            <p class="subtitle">Modern Web Applications Built with Latest Technologies</p>
        </div>

        <div class="portfolio-grid">
            <div class="portfolio-item">
                <div class="portfolio-image">🛒</div>
                <div class="portfolio-content">
                    <div class="portfolio-title">E-Commerce Platform</div>
                    <div class="portfolio-desc">Full-stack online store with payment integration and admin dashboard</div>
                    <div class="portfolio-tech">
                        <span class="tech-tag">React</span>
                        <span class="tech-tag">Next.js</span>
                        <span class="tech-tag">Stripe</span>
                    </div>
                </div>
                <div class="overlay">
                    <div>
                        <div>Complete e-commerce solution</div>
                        <div>✅ Payment Gateway</div>
                        <div>✅ Inventory Management</div>
                        <div>✅ Order Tracking</div>
                    </div>
                </div>
            </div>

            <div class="portfolio-item">
                <div class="portfolio-image">📊</div>
                <div class="portfolio-content">
                    <div class="portfolio-title">Analytics Dashboard</div>
                    <div class="portfolio-desc">Real-time data visualization with interactive charts and reports</div>
                    <div class="portfolio-tech">
                        <span class="tech-tag">TypeScript</span>
                        <span class="tech-tag">Chart.js</span>
                        <span class="tech-tag">PostgreSQL</span>
                    </div>
                </div>
                <div class="overlay">
                    <div>
                        <div>Business Intelligence Platform</div>
                        <div>✅ Real-time Analytics</div>
                        <div>✅ Custom Reports</div>
                        <div>✅ Data Export</div>
                    </div>
                </div>
            </div>

            <div class="portfolio-item">
                <div class="portfolio-image">💬</div>
                <div class="portfolio-content">
                    <div class="portfolio-title">Social Media App</div>
                    <div class="portfolio-desc">Modern social platform with real-time messaging and media sharing</div>
                    <div class="portfolio-tech">
                        <span class="tech-tag">React</span>
                        <span class="tech-tag">Socket.io</span>
                        <span class="tech-tag">MongoDB</span>
                    </div>
                </div>
                <div class="overlay">
                    <div>
                        <div>Social Networking Platform</div>
                        <div>✅ Real-time Chat</div>
                        <div>✅ Media Upload</div>
                        <div>✅ User Profiles</div>
                    </div>
                </div>
            </div>

            <div class="portfolio-item">
                <div class="portfolio-image">🏥</div>
                <div class="portfolio-content">
                    <div class="portfolio-title">Healthcare Portal</div>
                    <div class="portfolio-desc">Patient management system with appointment scheduling</div>
                    <div class="portfolio-tech">
                        <span class="tech-tag">Next.js</span>
                        <span class="tech-tag">Prisma</span>
                        <span class="tech-tag">Auth0</span>
                    </div>
                </div>
                <div class="overlay">
                    <div>
                        <div>Medical Management System</div>
                        <div>✅ Appointment Booking</div>
                        <div>✅ Patient Records</div>
                        <div>✅ HIPAA Compliant</div>
                    </div>
                </div>
            </div>

            <div class="portfolio-item">
                <div class="portfolio-image">🎓</div>
                <div class="portfolio-content">
                    <div class="portfolio-title">Learning Platform</div>
                    <div class="portfolio-desc">Online education platform with video streaming and progress tracking</div>
                    <div class="portfolio-tech">
                        <span class="tech-tag">React</span>
                        <span class="tech-tag">Video.js</span>
                        <span class="tech-tag">Supabase</span>
                    </div>
                </div>
                <div class="overlay">
                    <div>
                        <div>E-Learning Platform</div>
                        <div>✅ Video Streaming</div>
                        <div>✅ Progress Tracking</div>
                        <div>✅ Certificates</div>
                    </div>
                </div>
            </div>

            <div class="portfolio-item">
                <div class="portfolio-image">💼</div>
                <div class="portfolio-content">
                    <div class="portfolio-title">Business CRM</div>
                    <div class="portfolio-desc">Customer relationship management with sales pipeline and reporting</div>
                    <div class="portfolio-tech">
                        <span class="tech-tag">TypeScript</span>
                        <span class="tech-tag">Tailwind</span>
                        <span class="tech-tag">MySQL</span>
                    </div>
                </div>
                <div class="overlay">
                    <div>
                        <div>Customer Management System</div>
                        <div>✅ Sales Pipeline</div>
                        <div>✅ Lead Tracking</div>
                        <div>✅ Reports & Analytics</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="stats">
            <div class="stat">
                <span class="stat-number">500+</span>
                <span class="stat-label">Projects Completed</span>
            </div>
            <div class="stat">
                <span class="stat-number">98%</span>
                <span class="stat-label">Client Satisfaction</span>
            </div>
            <div class="stat">
                <span class="stat-number">24/7</span>
                <span class="stat-label">Support Available</span>
            </div>
            <div class="stat">
                <span class="stat-number">5.0★</span>
                <span class="stat-label">Average Rating</span>
            </div>
        </div>
    </div>
</body>
</html>
