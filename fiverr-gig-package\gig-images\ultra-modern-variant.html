<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ultra Modern React Developer - Gig Image</title>
    <link href="https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Space Grotesk', sans-serif;
            background: #0a0a0a;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .gig-container {
            width: 1200px;
            height: 675px;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            border-radius: 24px;
            position: relative;
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 
                0 0 0 1px rgba(255, 255, 255, 0.05),
                0 25px 50px rgba(0, 0, 0, 0.5),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }

        .grid-background {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
                linear-gradient(rgba(255, 255, 255, 0.03) 1px, transparent 1px),
                linear-gradient(90deg, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
            background-size: 50px 50px;
            animation: gridMove 20s linear infinite;
        }

        .neon-elements {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            overflow: hidden;
        }

        .neon-circle {
            position: absolute;
            border: 2px solid;
            border-radius: 50%;
            animation: neonPulse 4s ease-in-out infinite;
        }

        .neon-circle:nth-child(1) {
            width: 200px;
            height: 200px;
            top: 10%;
            right: 10%;
            border-color: #00f5ff;
            animation-delay: 0s;
        }

        .neon-circle:nth-child(2) {
            width: 150px;
            height: 150px;
            bottom: 20%;
            left: 15%;
            border-color: #ff006e;
            animation-delay: 1s;
        }

        .neon-circle:nth-child(3) {
            width: 100px;
            height: 100px;
            top: 60%;
            right: 25%;
            border-color: #8338ec;
            animation-delay: 2s;
        }

        .content {
            position: relative;
            z-index: 10;
            height: 100%;
            display: flex;
            flex-direction: column;
            padding: 50px;
        }

        .header-section {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 60px;
        }

        .title-area {
            flex: 1;
        }

        .status-badge {
            background: linear-gradient(135deg, #00f5ff, #0099cc);
            color: #000;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 20px;
            display: inline-block;
            box-shadow: 0 0 20px rgba(0, 245, 255, 0.5);
        }

        .main-heading {
            font-size: 72px;
            font-weight: 700;
            line-height: 0.9;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #ffffff 0%, #00f5ff 50%, #ff006e 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 30px rgba(255, 255, 255, 0.5);
        }

        .tagline {
            font-size: 20px;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 400;
            line-height: 1.4;
        }

        .price-display {
            text-align: center;
        }

        .price-tag {
            background: linear-gradient(135deg, #ff006e 0%, #8338ec 100%);
            color: white;
            padding: 30px 40px;
            border-radius: 20px;
            font-size: 36px;
            font-weight: 700;
            position: relative;
            box-shadow: 
                0 0 30px rgba(255, 0, 110, 0.6),
                0 20px 40px rgba(0, 0, 0, 0.3);
            animation: priceGlow 3s ease-in-out infinite;
        }

        .price-tag::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #ff006e, #8338ec, #00f5ff, #ff006e);
            border-radius: 20px;
            z-index: -1;
            animation: borderRotate 3s linear infinite;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 80px;
            flex: 1;
            align-items: center;
        }

        .tech-section {
            display: flex;
            flex-direction: column;
            gap: 30px;
        }

        .section-title {
            font-size: 24px;
            font-weight: 600;
            color: #fff;
            margin-bottom: 20px;
            text-transform: uppercase;
            letter-spacing: 2px;
        }

        .tech-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
        }

        .tech-item {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 12px;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .tech-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 245, 255, 0.1), transparent);
            transition: left 0.5s;
        }

        .tech-item:hover::before {
            left: 100%;
        }

        .tech-item:hover {
            transform: translateY(-5px);
            border-color: #00f5ff;
            box-shadow: 0 10px 30px rgba(0, 245, 255, 0.3);
        }

        .tech-emoji {
            font-size: 24px;
            margin-bottom: 8px;
            display: block;
        }

        .tech-title {
            color: #fff;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .tech-subtitle {
            color: rgba(255, 255, 255, 0.6);
            font-size: 12px;
        }

        .showcase-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 40px;
        }

        .code-window {
            width: 100%;
            max-width: 400px;
            background: rgba(0, 0, 0, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
        }

        .window-header {
            background: rgba(255, 255, 255, 0.1);
            padding: 12px 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .window-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }

        .dot-red { background: #ff5f56; }
        .dot-yellow { background: #ffbd2e; }
        .dot-green { background: #27ca3f; }

        .code-content {
            padding: 20px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.6;
        }

        .code-line {
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 4px;
        }

        .code-keyword { color: #ff006e; }
        .code-string { color: #00f5ff; }
        .code-function { color: #8338ec; }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            width: 100%;
        }

        .metric-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            padding: 24px 16px;
            border-radius: 12px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .metric-card:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.1);
        }

        .metric-number {
            font-size: 28px;
            font-weight: 700;
            color: #00f5ff;
            display: block;
            margin-bottom: 8px;
        }

        .metric-label {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 500;
        }

        .footer-section {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 40px;
        }

        .rating-display {
            display: flex;
            align-items: center;
            gap: 16px;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            padding: 16px 24px;
            border-radius: 25px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .stars {
            color: #ffd700;
            font-size: 20px;
            text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
        }

        .rating-info {
            color: #fff;
            font-weight: 600;
            font-size: 16px;
        }

        .action-button {
            background: linear-gradient(135deg, #00f5ff 0%, #0099cc 100%);
            color: #000;
            border: none;
            padding: 20px 40px;
            border-radius: 25px;
            font-size: 18px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            box-shadow: 0 0 30px rgba(0, 245, 255, 0.5);
        }

        .action-button:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 0 50px rgba(0, 245, 255, 0.8);
        }

        @keyframes gridMove {
            0% { transform: translate(0, 0); }
            100% { transform: translate(50px, 50px); }
        }

        @keyframes neonPulse {
            0%, 100% { 
                opacity: 0.5;
                transform: scale(1);
            }
            50% { 
                opacity: 1;
                transform: scale(1.1);
            }
        }

        @keyframes priceGlow {
            0%, 100% { 
                box-shadow: 
                    0 0 30px rgba(255, 0, 110, 0.6),
                    0 20px 40px rgba(0, 0, 0, 0.3);
            }
            50% { 
                box-shadow: 
                    0 0 50px rgba(255, 0, 110, 0.9),
                    0 25px 50px rgba(0, 0, 0, 0.4);
            }
        }

        @keyframes borderRotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="gig-container">
        <div class="grid-background"></div>
        
        <div class="neon-elements">
            <div class="neon-circle"></div>
            <div class="neon-circle"></div>
            <div class="neon-circle"></div>
        </div>

        <div class="content">
            <div class="header-section">
                <div class="title-area">
                    <div class="status-badge">● ONLINE NOW</div>
                    <h1 class="main-heading">REACT<br>EXPERT</h1>
                    <p class="tagline">Next.js 14 • TypeScript • Full Stack Development</p>
                </div>
                
                <div class="price-display">
                    <div class="price-tag">$25+</div>
                </div>
            </div>

            <div class="main-content">
                <div class="tech-section">
                    <h2 class="section-title">Tech Stack</h2>
                    <div class="tech-grid">
                        <div class="tech-item">
                            <span class="tech-emoji">⚛️</span>
                            <div class="tech-title">React 18</div>
                            <div class="tech-subtitle">Concurrent Features</div>
                        </div>
                        <div class="tech-item">
                            <span class="tech-emoji">🚀</span>
                            <div class="tech-title">Next.js 14</div>
                            <div class="tech-subtitle">App Router</div>
                        </div>
                        <div class="tech-item">
                            <span class="tech-emoji">📘</span>
                            <div class="tech-title">TypeScript</div>
                            <div class="tech-subtitle">Type Safety</div>
                        </div>
                        <div class="tech-item">
                            <span class="tech-emoji">🗄️</span>
                            <div class="tech-title">Database</div>
                            <div class="tech-subtitle">PostgreSQL</div>
                        </div>
                    </div>
                </div>

                <div class="showcase-section">
                    <div class="code-window">
                        <div class="window-header">
                            <div class="window-dot dot-red"></div>
                            <div class="window-dot dot-yellow"></div>
                            <div class="window-dot dot-green"></div>
                        </div>
                        <div class="code-content">
                            <div class="code-line"><span class="code-keyword">import</span> <span class="code-function">React</span> <span class="code-keyword">from</span> <span class="code-string">'react'</span></div>
                            <div class="code-line"><span class="code-keyword">import</span> <span class="code-function">NextAuth</span> <span class="code-keyword">from</span> <span class="code-string">'next-auth'</span></div>
                            <div class="code-line"></div>
                            <div class="code-line"><span class="code-keyword">export default function</span> <span class="code-function">App</span>() {</div>
                            <div class="code-line">  <span class="code-keyword">return</span> (</div>
                            <div class="code-line">    &lt;<span class="code-function">YourDreamApp</span> /&gt;</div>
                            <div class="code-line">  )</div>
                            <div class="code-line">}</div>
                        </div>
                    </div>

                    <div class="metrics-grid">
                        <div class="metric-card">
                            <span class="metric-number">500+</span>
                            <span class="metric-label">Projects</span>
                        </div>
                        <div class="metric-card">
                            <span class="metric-number">98%</span>
                            <span class="metric-label">Success Rate</span>
                        </div>
                        <div class="metric-card">
                            <span class="metric-number">24h</span>
                            <span class="metric-label">Delivery</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="footer-section">
                <div class="rating-display">
                    <div class="stars">★★★★★</div>
                    <div class="rating-info">5.0 • 500+ Reviews</div>
                </div>
                <button class="action-button">Start Project</button>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Staggered animation on load
            const animationSequence = [
                { selector: '.status-badge', delay: 0 },
                { selector: '.main-heading', delay: 200 },
                { selector: '.tagline', delay: 400 },
                { selector: '.price-tag', delay: 600 },
                { selector: '.tech-item', delay: 800 },
                { selector: '.code-window', delay: 1000 },
                { selector: '.metric-card', delay: 1200 },
                { selector: '.rating-display', delay: 1400 },
                { selector: '.action-button', delay: 1600 }
            ];

            animationSequence.forEach(({ selector, delay }) => {
                setTimeout(() => {
                    document.querySelectorAll(selector).forEach((el, index) => {
                        el.style.opacity = '0';
                        el.style.transform = 'translateY(30px)';
                        el.style.transition = 'all 0.8s cubic-bezier(0.4, 0, 0.2, 1)';
                        
                        setTimeout(() => {
                            el.style.opacity = '1';
                            el.style.transform = 'translateY(0)';
                        }, index * 100);
                    });
                }, delay);
            });

            // Interactive code typing effect
            setTimeout(() => {
                const codeLines = document.querySelectorAll('.code-line');
                codeLines.forEach((line, index) => {
                    setTimeout(() => {
                        line.style.opacity = '0';
                        line.style.transform = 'translateX(-20px)';
                        line.style.transition = 'all 0.5s ease';
                        
                        setTimeout(() => {
                            line.style.opacity = '1';
                            line.style.transform = 'translateX(0)';
                        }, 50);
                    }, index * 100);
                });
            }, 1200);
        });
    </script>
</body>
</html>
